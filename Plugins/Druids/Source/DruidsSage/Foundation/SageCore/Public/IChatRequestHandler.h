#pragma once

#include <CoreMinimal.h>

class IDruidsSageChatItem;

class IChatRequestHandler
{
public:
    virtual ~IChatRequestHandler() = default;

    virtual bool IsNoActiveRequest() const = 0;
    virtual void StopAndCleanupRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems) = 0;
    virtual void SetupAndSendRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems,
                                     const TSharedPtr<IDruidsSageChatItem>& AssistantMessage, const FString& Context) = 0;
};