#include "UMG/DruidsSageChatView.h"
#include "UMG/DruidsChatWidgetManager.h"
#include "UMG/DruidsChatWidgetFactory.h"
#include "UMG/DruidsChatItemInterface.h"
#include "IChatRequestHandler.h"
#include "ISageExtensionDelegator.h"
#include "Components/ScrollBox.h"
#include "Components/VerticalBox.h"
#include "Components/MultiLineEditableTextBox.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "Blueprint/UserWidget.h"
#include "Misc/Paths.h"
#include "HAL/PlatformFilemanager.h"
#include "LogDruids.h"

// Static constants
const FString UDruidsSageChatView::HistoryFileExtension = TEXT(".history");
const int32 UDruidsSageChatView::MaxHistoryItems = 1000;

UDruidsSageChatView::UDruidsSageChatView(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    bIsInitialized = false;
    bIsStreaming = false;
    CurrentStreamingItem = nullptr;
}

void UDruidsSageChatView::NativeConstruct()
{
    Super::NativeConstruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::NativeConstruct - Constructing chat view"));
    
    InitializeChatView();
    
    // Set up button events
    if (SendButton)
    {
        SendButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::OnSendButtonClicked);
    }
    
    if (ClearButton)
    {
        ClearButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::OnClearButtonClicked);
    }
    
    // Set up input events
    if (MessageInputBox)
    {
        MessageInputBox->OnTextChanged.AddDynamic(this, &UDruidsSageChatView::OnMessageInputTextChanged);
        MessageInputBox->OnTextCommitted.AddDynamic(this, &UDruidsSageChatView::OnMessageInputTextCommitted);
    }
    
    // Update initial UI state
    UpdateSendButtonState();
    UpdateContextDisplay();
    
    bIsInitialized = true;
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::NativeConstruct - Chat view constructed successfully"));
}

void UDruidsSageChatView::NativeDestruct()
{
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::NativeDestruct - Destructing chat view"));
    
    // Save chat history
    SaveChatHistory();
    
    CleanupChatView();
    
    // Clean up event bindings
    if (SendButton)
    {
        SendButton->OnClicked.RemoveAll(this);
    }
    
    if (ClearButton)
    {
        ClearButton->OnClicked.RemoveAll(this);
    }
    
    if (MessageInputBox)
    {
        MessageInputBox->OnTextChanged.RemoveAll(this);
        MessageInputBox->OnTextCommitted.RemoveAll(this);
    }
    
    bIsInitialized = false;
    
    Super::NativeDestruct();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::NativeDestruct - Chat view destructed"));
}

void UDruidsSageChatView::SendMessage(const FString& MessageText, EDruidsSageChatRole Role)
{
    if (!IsValidMessage(MessageText))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatView::SendMessage - Invalid message text"));
        return;
    }
    
    ProcessMessageSending(MessageText);
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SendMessage - Sent message with role %d"), (int32)Role);
}

void UDruidsSageChatView::ClearChat()
{
    // Clear chat items from UI
    if (ChatMessageContainer)
    {
        ChatMessageContainer->ClearChildren();
    }
    
    // Clear internal arrays
    ChatItems.Empty();
    MessageHistory.Empty();
    
    // Reset streaming state
    bIsStreaming = false;
    CurrentStreamingItem = nullptr;
    
    // Notify Blueprint
    OnChatCleared();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::ClearChat - Chat cleared"));
}

void UDruidsSageChatView::SetTabContext(const FString& Context, const FString& DisplayMessage)
{
    if (CurrentTabContext != Context)
    {
        CurrentTabContext = Context;
        CurrentContextDisplay = DisplayMessage;
        
        UpdateContextDisplay();
        
        OnContextChanged.Broadcast(Context, DisplayMessage);
        OnContextChangedEvent(Context, DisplayMessage);
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SetTabContext - Tab context set: %s"), *Context);
    }
}

void UDruidsSageChatView::SetBlueprintContext(const FString& Context, const FString& DisplayMessage)
{
    if (CurrentBlueprintContext != Context)
    {
        CurrentBlueprintContext = Context;
        CurrentContextDisplay = DisplayMessage;
        
        UpdateContextDisplay();
        
        OnContextChanged.Broadcast(Context, DisplayMessage);
        OnContextChangedEvent(Context, DisplayMessage);
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SetBlueprintContext - Blueprint context set: %s"), *Context);
    }
}

void UDruidsSageChatView::SetSessionID(const FString& NewSessionID)
{
    if (SessionID != NewSessionID)
    {
        // Save current session before switching
        if (!SessionID.IsEmpty())
        {
            SaveChatHistory();
        }
        
        SessionID = NewSessionID;
        
        // Clear current chat and load new session
        ClearChat();
        LoadChatHistory();
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SetSessionID - Session ID set to: %s"), *SessionID);
    }
}

void UDruidsSageChatView::SetChatRequestHandler(const TSharedPtr<IChatRequestHandler>& Handler)
{
    ChatRequestHandler = Handler;
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SetChatRequestHandler - Chat request handler set"));
}

void UDruidsSageChatView::SetExtensionDelegator(const TSharedPtr<ISageExtensionDelegator>& Delegator)
{
    ExtensionDelegator = Delegator;
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SetExtensionDelegator - Extension delegator set"));
}

void UDruidsSageChatView::LoadChatHistory()
{
    if (SessionID.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatView::LoadChatHistory - No session ID set"));
        return;
    }
    
    // Implementation would load chat history from file
    // For now, just notify Blueprint
    OnChatHistoryLoaded();
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::LoadChatHistory - Chat history loaded for session: %s"), *SessionID);
}

void UDruidsSageChatView::SaveChatHistory()
{
    if (SessionID.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatView::SaveChatHistory - No session ID set"));
        return;
    }
    
    // Implementation would save chat history to file
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::SaveChatHistory - Chat history saved for session: %s"), *SessionID);
}

void UDruidsSageChatView::OnSendButtonClicked()
{
    if (MessageInputBox)
    {
        FString MessageText = MessageInputBox->GetText().ToString();
        if (!MessageText.IsEmpty())
        {
            SendMessage(MessageText);
            MessageInputBox->SetText(FText::GetEmpty());
            FocusMessageInput();
        }
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatView::OnSendButtonClicked - Send button clicked"));
}

void UDruidsSageChatView::OnClearButtonClicked()
{
    ClearChat();
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageChatView::OnClearButtonClicked - Clear button clicked"));
}

void UDruidsSageChatView::OnMessageInputTextChanged(const FText& Text)
{
    UpdateSendButtonState();
}

void UDruidsSageChatView::OnMessageInputTextCommitted(const FText& Text, ETextCommit::Type CommitMethod)
{
    if (CommitMethod == ETextCommit::OnEnter)
    {
        OnSendButtonClicked();
    }
}

void UDruidsSageChatView::AddChatItem(UUserWidget* ChatItem)
{
    if (!ChatItem || !ChatMessageContainer)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsSageChatView::AddChatItem - Invalid chat item or container"));
        return;
    }
    
    // Add to container
    ChatMessageContainer->AddChild(ChatItem);
    
    // Add to tracking array
    ChatItems.Add(ChatItem);
    
    // Scroll to bottom
    ScrollToBottom();
    
    // Notify Blueprint
    OnChatItemCreated(ChatItem);
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatView::AddChatItem - Chat item added, total items: %d"), ChatItems.Num());
}

void UDruidsSageChatView::AddMessageToHistory(const FDruidsSageChatMessage& Message)
{
    MessageHistory.Add(Message);
    
    // Limit history size
    if (MessageHistory.Num() > MaxHistoryItems)
    {
        MessageHistory.RemoveAt(0, MessageHistory.Num() - MaxHistoryItems);
    }
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatView::AddMessageToHistory - Message added to history, total: %d"), MessageHistory.Num());
}

UUserWidget* UDruidsSageChatView::CreateChatItemInternal(EDruidsSageChatRole Role, const FDruidsSageChatMessage& Message)
{
    // Try Blueprint implementation first
    UUserWidget* BlueprintChatItem = CreateChatItem(Role, Message);
    if (BlueprintChatItem)
    {
        return BlueprintChatItem;
    }
    
    // Fall back to factory creation
    if (UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(this))
    {
        if (UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory())
        {
            UUserWidget* ChatItem = Factory->CreateChatItem(Role, this);
            if (ChatItem)
            {
                // Set message content if the widget implements the interface
                if (IDruidsChatItemInterface* ChatItemInterface = Cast<IDruidsChatItemInterface>(ChatItem))
                {
                    ChatItemInterface->SetMessageContent_Native(Message);
                }
                
                return ChatItem;
            }
        }
    }
    
    UE_LOG(LogDruidsSage, Error, TEXT("UDruidsSageChatView::CreateChatItemInternal - Failed to create chat item"));
    return nullptr;
}

void UDruidsSageChatView::UpdateContextDisplay()
{
    if (ContextLabel)
    {
        FString ContextText = GetCurrentContextString();
        ContextLabel->SetText(FText::FromString(ContextText));
    }
}

FString UDruidsSageChatView::GetCurrentContextString() const
{
    TArray<FString> ContextParts;
    
    if (!CurrentTabContext.IsEmpty())
    {
        ContextParts.Add(FString::Printf(TEXT("Tab: %s"), *CurrentTabContext));
    }
    
    if (!CurrentBlueprintContext.IsEmpty())
    {
        ContextParts.Add(FString::Printf(TEXT("Blueprint: %s"), *CurrentBlueprintContext));
    }
    
    if (ContextParts.IsEmpty())
    {
        return TEXT("No context");
    }
    
    return FString::Join(ContextParts, TEXT(" | "));
}

void UDruidsSageChatView::ScrollToBottom()
{
    if (ChatScrollBox)
    {
        ChatScrollBox->ScrollToEnd();
    }
}

void UDruidsSageChatView::FocusMessageInput()
{
    if (MessageInputBox)
    {
        MessageInputBox->SetKeyboardFocus();
    }
}

void UDruidsSageChatView::UpdateSendButtonState()
{
    if (SendButton && MessageInputBox)
    {
        bool bHasText = !MessageInputBox->GetText().IsEmpty();
        SendButton->SetIsEnabled(bHasText && !bIsStreaming);
    }
}

void UDruidsSageChatView::InitializeChatView()
{
    // Initialize chat view specific functionality
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatView::InitializeChatView - Initializing chat view"));
}

void UDruidsSageChatView::CleanupChatView()
{
    // Cleanup chat view specific functionality
    bIsStreaming = false;
    CurrentStreamingItem = nullptr;
    
    UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsSageChatView::CleanupChatView - Cleaning up chat view"));
}

void UDruidsSageChatView::ProcessMessageSending(const FString& MessageText)
{
    // Create user message
    FDruidsSageChatMessage UserMessage;
    UserMessage.SetRole(EDruidsSageChatRole::User);
    UserMessage.SetChatContent(MessageText);
    
    // Add to history
    AddMessageToHistory(UserMessage);
    
    // Create and add chat item
    UUserWidget* UserChatItem = CreateChatItemInternal(EDruidsSageChatRole::User, UserMessage);
    if (UserChatItem)
    {
        AddChatItem(UserChatItem);
    }
    
    // Notify events
    OnMessageSending.Broadcast();
    OnMessageReceived.Broadcast(UserMessage);
    OnMessageReceivedEvent(UserMessage);
    
    // Send to chat request handler if available
    if (ChatRequestHandler.IsValid())
    {
        // Implementation would send the message to the AI service
    }
}

void UDruidsSageChatView::HandleStreamingResponse(const FString& PartialContent)
{
    if (CurrentStreamingItem)
    {
        // Update streaming item with partial content
        if (IDruidsChatItemInterface* ChatItemInterface = Cast<IDruidsChatItemInterface>(CurrentStreamingItem))
        {
            ChatItemInterface->UpdateFromStreamingContent_Native(PartialContent);
        }
        
        OnStreamingUpdate.Broadcast(PartialContent);
        OnStreamingUpdateEvent(PartialContent);
    }
}

FString UDruidsSageChatView::GetHistoryFilePath() const
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage/Sessions"), SessionID + HistoryFileExtension);
}

bool UDruidsSageChatView::IsValidMessage(const FString& MessageText) const
{
    return !MessageText.IsEmpty() && MessageText.TrimStartAndEnd().Len() > 0;
}
