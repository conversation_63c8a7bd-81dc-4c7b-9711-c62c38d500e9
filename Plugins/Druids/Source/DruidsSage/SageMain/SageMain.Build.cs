using UnrealBuildTool;

public class SageMain : ModuleRules
{
    public SageMain(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                
                "Json",
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
        {
            "DruidsCore",
            
            "SageCommonTypes",
            "SageCore",

            "SageNetworking",
            "SageUI",
            "SageExtensions",
            
            "DruidsSageEditorModule",
        });
    }
}